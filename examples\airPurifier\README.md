# Tuya Panel AirPurifier Template

English | [简体中文](./README-zh_CN.md)

for docs, please visit [tuya docs](https://docs.tuya.com)

for users outside Chinese mainland, please remove `.npmrc` file.

## Download manually

```bash
$ curl https://codeload.github.com/tuya/tuya-panel-demo/tar.gz/master | tar -xz --strip=2 tuya-panel-demo-master/examples/airPurifier
$ mv basic tuya-panel-airPurifier-example
$ cd tuya-panel-airPurifier-example
```

## Introduction

This template project can be used to quickly start the air purifier project and supports the following functions: **wind speed adjustment**, **mode switching**, **geographic location and indoor and outdoor weather data display**, **equipment and specific functions Switch control**, **Dynamic configuration item expansion function adaptation**.

Support category: WiFi air purifier.

You can scan the following QR code through the Tuya app to preview.

![AirPurifier](https://images.tuyacn.com/fe-static/docs/img/2d7d2936-84bb-43d5-8244-aff29d76e3c5.png?tyName=airPurifier.png)

## Running

```bash
$ npm install && npm run start
# or
$ yarn && yarn start
```

## License

Copyright © 2020
