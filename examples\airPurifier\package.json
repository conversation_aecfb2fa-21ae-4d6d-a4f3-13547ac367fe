{"rnVersion": "5.30", "license": "MIT", "name": "airPurifier", "pid": [], "uiid": "", "version": "1.0.0", "scripts": {"start": "node node_modules/react-native/local-cli/cli.js start", "lint": "prettier --write '**/*.{js,jsx,ts,tsx}'", "test": "echo \"Error: no test specified\" && exit 1", "readResource": "node scripts/read.js", "postinstall": "patch-package --patch-dir node_modules/@tuya/tuya-panel-patches/patches", "release:init": "standard-version --first-release -t @template/airPurifier@", "release:major:pre": "standard-version --release-as major --prerelease rc --skip.tag=true", "release:major": "standard-version --release-as major -t @template/airPurifier@", "release:minor:pre": "standard-version --release-as minor --prerelease rc --skip.changelog=true --skip.tag=true", "release:minor": "standard-version --release-as minor -t @template/airPurifier@", "release:patch:pre": "standard-version --release-as patch --prerelease rc --skip.tag=true", "release:patch": "standard-version --release-as patch -t @template/airPurifier@"}, "main": "index.ios.js", "dependencies": {"camelcase": "^5.0.0", "events": "^1.1.1", "lodash": "^4.17.19", "moment": "^2.20.1", "prop-types": "^15.6.1", "react": "16.8.3", "react-addons-shallow-compare": "^15.6.2", "react-native": "0.59.10", "react-native-svg": "5.5.1", "react-redux": "^7.2.1", "redux": "^4.0.0", "redux-actions": "^2.6.1", "redux-logger": "^3.0.6", "redux-observable": "^1.0.0", "redux-thunk": "^2.3.0", "rxjs": "^6.3.1", "rxjs-compat": "^6.3.1", "style-equal": "^1.0.0", "tuya-panel-kit": "~4.7.8"}, "devDependencies": {"@babel/plugin-proposal-decorators": "^7.12.1", "@commitlint/cli": "^11.0.0", "@tuya/tuya-panel-patches": "^0.59.10", "babel-jest": "^22.1.0", "babel-plugin-import": "^1.11.0", "babel-plugin-module-resolver": "^4.0.0", "babel-plugin-transform-decorators-legacy": "^1.3.4", "babel-plugin-transform-remove-console": "^6.9.4", "commitizen": "^4.2.2", "commitlint-config-cz": "^0.13.2", "cz-customizable": "^6.3.0", "eslint-config-airbnb": "^18.2.1", "eslint-config-tuya": "^1.0.0", "eslint-import-resolver-react-native": "^0.2.0", "eslint-plugin-react-native": "^3.10.0", "husky": "^4.3.0", "jest": "^22.1.1", "metro-react-native-babel-preset": "^0.63.0", "react-test-renderer": "^16.2.0", "standard-version": "^9.0.0"}, "jest": {"preset": "react-native"}}