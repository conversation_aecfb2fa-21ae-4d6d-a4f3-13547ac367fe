module.exports = {
  en: {
    today: 'Today',
    yesterday: 'Yesterday',
    schedule: 'Schedule',
    pm25: 'PM2.5',
    power: 'Power',
    setting: 'Setting',
    indoorAirQualityTip: 'Indoor Air Quality: {0}',
    filterTip: 'Filter Remains {0}%',
    powerOffTip: 'Power Off',
    speedTip: 'Speed: {0}',
    timeTip: 'Air Purifier will be shutdown after {0} Minutes',
    countdownTip: 'Air Purifier will be shutdown after {0} Hours {1} Minutes',
    outdoorPM25Tip: 'Outdoor PM2.5 {0}',
    indoorPM25Tip: 'Indoor PM2.5 {0}',
    filterResetTip: 'Confirm reset filter ?',
    publishErrorTip: 'Out of range',
    dp_pm25: 'PM2.5',
    dp_mode: 'Mode',
    dp_speed: 'Speed',
    dp_countdown: 'Countdown',
    dp_temp: 'Temp',
    dp_humidity: 'Humidity',
    dp_tvoc: 'TVOC',
    dp_eco2: 'ECO2',
    dp_uv: 'UV',
    dp_wet: 'Wet',
    dp_filter_days: 'Filter Days',
    dp_total_time: 'Total Time',
    dp_total_pm: 'Total PM',
    dp_filter_reset: 'Filter Reset',
    dp_temp_unit: '℃',
    dp_humidity_unit: '％',
    dp_tvoc_unit: '',
    dp_eco2_unit: '',
    dp_filter_days_unit: 'd',
    dp_total_time_unit: 'd',
    dp_total_pm_unit1: 'mg',
    dp_total_pm_unit2: 'g',
    dp_mode_1: 'Mode 1',
    dp_mode_2: 'Mode 2',
    dp_mode_3: 'Mode 3',
    dp_mode_4: 'Mode 4',
    dp_mode_5: 'Mode 5',
    dp_speed_1: 'Speed 1',
    dp_speed_2: 'Speed 2',
    dp_speed_3: 'Speed 3',
    dp_speed_4: 'Speed 4',
    dp_speed_5: 'Speed 5',
    dp_air_quality_1: 'Air Quality 1',
    dp_air_quality_2: 'Air Quality 2',
    dp_air_quality_3: 'Air Quality 3',
    dp_air_quality_4: 'Air Quality 4',
    dp_air_quality_5: 'Air Quality 5',
    dp_countdown_1: '1 Hour',
    dp_countdown_2: '2 Hours',
    dp_countdown_3: '3 Hours',
    dp_countdown_4: '4 Hours',
    dp_countdown_5: '5 Hours',
    dp_countdown_cancel: 'Cancel',
  },

  zh: {
    today: '今天',
    yesterday: '昨天',
    schedule: '定时',
    pm25: 'PM2.5',
    power: '开关',
    setting: '设置',
    indoorAirQualityTip: '室内空气: {0}',
    filterTip: '滤芯还剩 {0}%',
    powerOffTip: '已关机',
    speedTip: '风速: {0}',
    timeTip: '空气净化器将在{0}分钟后关闭',
    countdownTip: '空气净化器将在{0}小时{1}分钟后关闭',
    outdoorPM25Tip: '室外 PM2.5 {0}',
    indoorPM25Tip: '室内 PM2.5 {0}',
    filterResetTip: '是否确认复位滤芯',
    publishErrorTip: '超出下发范围',
    dp_pm25: 'PM2.5',
    dp_mode: '模式',
    dp_speed: '风速',
    dp_countdown: '倒计时',
    dp_temp: '温度',
    dp_humidity: '湿度',
    dp_tvoc: 'TVOC',
    dp_eco2: 'ECO2',
    dp_uv: 'UV杀菌',
    dp_wet: '加湿',
    dp_filter_days: '滤芯剩余天数',
    dp_total_time: '累计运行时间',
    dp_total_pm: '累计吸收颗粒',
    dp_filter_reset: '滤芯复位',
    dp_temp_unit: '℃',
    dp_humidity_unit: '％',
    dp_tvoc_unit: '',
    dp_eco2_unit: '',
    dp_filter_days_unit: 'd',
    dp_total_time_unit: 'd',
    dp_total_pm_unit1: 'mg',
    dp_total_pm_unit2: 'g',
    dp_mode_1: 'Mode 1',
    dp_mode_2: 'Mode 2',
    dp_mode_3: 'Mode 3',
    dp_mode_4: 'Mode 4',
    dp_mode_5: 'Mode 5',
    dp_speed_1: 'Speed 1',
    dp_speed_2: 'Speed 2',
    dp_speed_3: 'Speed 3',
    dp_speed_4: 'Speed 4',
    dp_speed_5: 'Speed 5',
    dp_air_quality_1: 'Air Quality 1',
    dp_air_quality_2: 'Air Quality 2',
    dp_air_quality_3: 'Air Quality 3',
    dp_air_quality_4: 'Air Quality 4',
    dp_air_quality_5: 'Air Quality 5',
    dp_countdown_1: '1 小时',
    dp_countdown_2: '2 小时',
    dp_countdown_3: '3 小时',
    dp_countdown_4: '4 小时',
    dp_countdown_5: '5 小时',
    dp_countdown_cancel: '取消',
  },
};
